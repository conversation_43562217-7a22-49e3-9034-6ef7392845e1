@import '../global';

.mainContainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: $backgroundColor;
    overflow: hidden;
}

.noPropertyMessage {
    text-align: center;
    color: $secondaryColor;
    font-size: 18px;
    margin: 20px 0;
    padding: 20px;
}

.pdfViewerArea {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px;
    background-color: $white;
    border: $smallBorderWidth solid $primaryColor;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.waitingMessage {
    color: $secondaryColor;
    font-size: 18px;
    text-align: center;
    padding: 40px;
}

.pdfViewer {
    width: 100%;
    height: 100%;
    border: none;
    background-color: $white;
}

// Footer (matching JC_FormTablet footer structure)
.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: $white;
    border-top: $smallBorderWidth solid $primaryColor;
    min-height: 60px;
    gap: 15px;

    .leftFooterContainer {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .rightFooterContainer {
        display: flex;
        align-items: center;
        gap: 15px;
    }
}

// Mobile responsiveness
@media (max-width: 768px) {
    .mainContainer {
        height: 100vh;
    }

    .pdfViewerArea {
        margin: 10px;
    }

    .footer {
        padding: 10px 15px;
        flex-direction: column;
        gap: 10px;

        .leftFooterContainer,
        .rightFooterContainer {
            width: 100%;
            justify-content: center;
        }
    }

    .waitingMessage {
        font-size: 16px;
        padding: 20px;
    }
}
